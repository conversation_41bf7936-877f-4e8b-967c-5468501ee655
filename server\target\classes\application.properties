spring.application.name=server

# Server Configuration
server.port=8080

# Database Configuration (H2 for development)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
app.jwt.secret=mySecretKeyForDigitalPavtiPustakApplicationThatIsLongEnoughForJWTSecurity
app.jwt.expiration=86400000

# Logging
logging.level.com.app.server=DEBUG
logging.level.org.springframework.security=DEBUG

# MySQL Configuration (uncomment for production)
# spring.datasource.url=************************************************
# spring.datasource.username=root
# spring.datasource.password=yourpassword
# spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
# spring.jpa.hibernate.ddl-auto=update
