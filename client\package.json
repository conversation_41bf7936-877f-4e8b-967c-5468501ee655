{"name": "client", "license": "0BSD", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/masked-view": "^0.1.11", "@react-native-masked-view/masked-view": "^0.3.2", "@react-native-picker/picker": "2.11.1", "@react-navigation/drawer": "^7.5.8", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.26", "@react-navigation/stack": "^7.4.8", "expo": "~53.0.22", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.6", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "~1.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}